
import React from 'react';
import { HashRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import CreatePostPage from './pages/CreatePostPage';
import EditPostPage from './pages/EditPostPage';
import ProfilePage from './pages/ProfilePage';
import SignupPage from './pages/SignupPage';
import AdminPage from './pages/AdminPage';
import AdminAnalyticsPage from './pages/AdminAnalyticsPage'; // Added
import NotFoundPage from './pages/NotFoundPage';
import LoginPage from './pages/LoginPage'; // Added
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './hooks/useAuth'; // Corrected import path
import { PostsProvider } from './contexts/PostsContext';
import { APP_NAME, APP_TAGLINE } from './constants';

const AppContent: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const location = useLocation();

  const publicPaths = ['/login', '/signup'];
  const isPublicPath = publicPaths.includes(location.pathname);

  if (!currentUser && !isPublicPath) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (currentUser && location.pathname === '/login') {
    return <Navigate to="/" replace />;
  }

  if (isPublicPath) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup" element={<SignupPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  // Authenticated Routes
  return (
    <div className="flex flex-col min-h-screen bg-neutral-base"> {/* Matrix rain background handled by body/canvas globally */}
      <Navbar />
      <main className="flex-grow container mx-auto pt-4 pb-8 px-2 sm:px-4"> {/* Added horizontal padding */}
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/create" element={<CreatePostPage />} />
          <Route path="/edit-post/:postId" element={<EditPostPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          
          {/* Admin Routes */}
          <Route 
            path="/admin" 
            element={isAdmin ? <AdminPage /> : <Navigate to="/404" replace />} 
          />
          <Route 
            path="/admin/analytics" 
            element={isAdmin ? <AdminAnalyticsPage /> : <Navigate to="/404" replace />} 
          />

          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </main>
      <footer className="bg-neutral-surface text-center p-4 border-t border-neutral-border">
        <p className="text-sm text-neutral-muted">&copy; {new Date().getFullYear()} {APP_NAME}. {APP_TAGLINE}.</p>
      </footer>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <PostsProvider>
        <HashRouter>
          <AppContent />
        </HashRouter>
      </PostsProvider>
    </AuthProvider>
  );
};

export default App;