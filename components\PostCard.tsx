import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Post } from '../types';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import HeartIcon from './icons/HeartIcon';
import ChatBubbleIcon from './icons/ChatBubbleIcon';
import ShareIcon from './icons/ShareIcon';
import TrashIcon from './icons/TrashIcon';
import PencilIcon from './icons/PencilIcon';

interface PostCardProps {
  post: Post;
}

const PostCard: React.FC<PostCardProps> = ({ post }) => {
  const { currentUser, isAdmin } = useAuth();
  const { deletePost, toggleLikePost, addCommentToPost, deleteComment } = usePosts();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');

  // Determine display name and avatar for the post
  const displayUsername = post.isAnonymous && !isAdmin ? 'Anonymous' : post.username;
  const displayAvatarUrl = post.isAnonymous && !isAdmin ? 'https://picsum.photos/seed/anonymous/100/100' : post.userAvatarUrl;

  const timeSince = (dateString: string): string => {
    const date = new Date(dateString);
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + "y";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + "mo";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + "d";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + "h";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + "m";
    return Math.floor(seconds) + "s";
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim() && currentUser) {
      addCommentToPost(post.id, newComment.trim(), currentUser);
      setNewComment('');
    }
  };

  const canEditOrDelete = isAdmin || currentUser?.id === post.userId || (post.isAnonymous && currentUser?.id === post.actualUserId);

  return (
    <article className="bg-neutral-surface rounded-lg shadow-xl overflow-hidden border border-neutral-border my-4 animate-slide-up hover-glow transition-all duration-300 hover:scale-[1.02] cyber-border">
      <header className="p-4 flex items-center justify-between">
        <div className="flex items-center">
          <img src={displayAvatarUrl} alt={displayUsername} className="w-10 h-10 rounded-full mr-3 border-2 border-brand-primary hover-scale transition-transform duration-200" />
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-semibold text-neutral-100">{displayUsername}</p>
              {post.isAnonymous && isAdmin && (
                <span className="text-xs bg-accent-warning/20 text-yellow-400 px-2 py-0.5 rounded-full">
                  Actually: {post.username}
                </span>
              )}
            </div>
            <p className="text-xs text-neutral-muted">{timeSince(post.timestamp)} ago</p>
          </div>
        </div>
        {canEditOrDelete && (
          <div className="flex items-center space-x-2">
            <Link
              to={`/edit-post/${post.id}`}
              className="text-neutral-muted hover:text-brand-primary p-1 rounded-md transition-all duration-200 hover-scale hover-text-glow"
              title="Edit Post"
            >
              <PencilIcon className="w-5 h-5" />
            </Link>
            <button
              onClick={() => deletePost(post.id)}
              className="text-accent-error hover:text-red-400 p-1 rounded-md transition-all duration-200 hover-scale animate-cyber-flicker"
              title="Delete Post"
            >
              <TrashIcon className="w-5 h-5" />
            </button>
          </div>
        )}
      </header>

      {post.imageUrl && (
        <img
          src={post.imageUrl}
          alt={`Post by ${post.username}: ${post.caption}`}
          className="w-full h-auto object-cover max-h-[70vh]"
          onError={(e) => e.currentTarget.style.display='none'} // Hide if image fails to load
        />
      )}

      <div className="p-4">
        {post.caption && (
            <p className="text-neutral-200 text-sm italic mb-2 whitespace-pre-wrap">{post.caption}</p>
        )}
        {post.contentBody && (
           <p className="text-neutral-100 my-3 whitespace-pre-wrap">{post.contentBody}</p>
        )}

        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {post.tags.map(tag => (
              <span key={tag} className="text-xs bg-neutral-base text-brand-primary px-2 py-0.5 rounded-full">
                #{tag}
              </span>
            ))}
          </div>
        )}

        <div className="flex items-center space-x-4 text-neutral-muted mb-3 pt-3 border-t border-neutral-border">
          <button
            onClick={() => toggleLikePost(post.id)}
            className={`flex items-center space-x-1 hover:text-red-500 transition-all duration-200 hover-scale hover-text-glow ${post.isLikedByCurrentUser ? 'text-red-500 animate-pulse-glow' : ''}`}
            title={post.isLikedByCurrentUser ? "Unlike" : "Like"}
            disabled={!currentUser}
          >
            <HeartIcon className="w-6 h-6" isFilled={post.isLikedByCurrentUser} /> <span>{post.likes}</span>
          </button>
          <button onClick={() => setShowComments(!showComments)} className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow" title="Comments">
            <ChatBubbleIcon className="w-6 h-6" /> <span>{post.comments.length}</span>
          </button>
          <button className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow" title="Share (Placeholder)">
            <ShareIcon className="w-6 h-6" />
          </button>
        </div>

        {showComments && (
          <div className="mt-4 space-y-3 pt-3 border-t border-neutral-border">
            {post.comments.length === 0 && <p className="text-sm text-neutral-muted">No comments yet.</p>}
            {post.comments.map(comment => (
              <div key={comment.id} className="flex items-start space-x-2 text-sm">
                <img src={comment.avatarUrl} alt={comment.username} className="w-6 h-6 rounded-full mt-0.5" />
                <div className="flex-grow bg-neutral-base p-2 rounded-md">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-neutral-100">{comment.username}</span>
                    <span className="text-xs text-neutral-muted">{timeSince(comment.timestamp)}</span>
                  </div>
                  <p className="text-neutral-200 mt-0.5 whitespace-pre-wrap">{comment.text}</p>
                </div>
                 {(isAdmin || currentUser?.id === comment.userId) && (
                  <button
                    onClick={() => deleteComment(post.id, comment.id)}
                    className="text-accent-error hover:text-red-400 p-1 rounded-md transition-colors self-center ml-1"
                    title="Delete Comment"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
            {currentUser && (
              <form onSubmit={handleCommentSubmit} className="flex items-center space-x-2 mt-3">
                <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-8 h-8 rounded-full" />
                <input
                  type="text"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="flex-grow bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-1 focus:ring-brand-primary focus:border-brand-primary text-sm"
                />
                <button type="submit" className="bg-brand-primary hover:bg-brand-secondary text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Post</button>
              </form>
            )}
            {!currentUser && <p className="text-sm text-neutral-muted mt-2">Please log in to comment.</p>}
          </div>
        )}
      </div>
    </article>
  );
};

export default PostCard;