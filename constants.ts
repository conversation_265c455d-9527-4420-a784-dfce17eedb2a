import { User, Post, ADMIN_USER_ID, LoginScreenConfig } from './types';
import { v4 as uuidv4 } from 'uuid';

export const APP_NAME = "S3Kt0R-Gram";
export const APP_TAGLINE = "Make Information De-Centralized Again";

export const DEFAULT_LOGIN_SCREEN_CONFIG: LoginScreenConfig = {
  message: "Welcome to S3Kt0R-Gram. The truth is in the code. Choose your path.",
  imageUrl: "https://images.unsplash.com/photo-1505682499293-230dd9df9655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80", // A default thematic image
  imageOverlayOpacity: 0.5,
};

export const LOGIN_SCREEN_CONFIG_KEY = 's3kt0rGramLoginConfig';


export const DEFAULT_USERS: User[] = [
  { id: 'user1_anon_voice', username: 'AnonVoice_77', avatarUrl: 'https://picsum.photos/seed/anonvoice/100/100', isActive: true, isPendingApproval: false },
  { id: 'user2_cypher_punk', username: 'CypherDude', avatarUrl: 'https://picsum.photos/seed/cypherdude/100/100', isActive: true, isPendingApproval: false },
  { id: 'user3_truth_seeker', username: 'JustTheFacts', avatarUrl: 'https://picsum.photos/seed/truthseeker/100/100', isActive: true, isPendingApproval: false },
  { id: ADMIN_USER_ID, username: 'S3Kt0R_Overseer', avatarUrl: 'https://picsum.photos/seed/admin_s3ktor/100/100', isActive: true, isPendingApproval: false },
];

export const MOCK_POSTS: Post[] = [
  {
    id: uuidv4(),
    userId: DEFAULT_USERS[0].id,
    username: DEFAULT_USERS[0].username,
    userAvatarUrl: DEFAULT_USERS[0].avatarUrl,
    imageUrl: 'https://picsum.photos/seed/post1_rebel/600/400',
    caption: 'System critical. Time to rebuild. #Decentralize #TruthWillOut',
    contentBody: "The current structures are failing us. We need to look towards new paradigms of organization and information sharing. This isn't just about technology; it's about a fundamental shift in how we perceive control and freedom. Join the movement, question everything.",
    likes: 66,
    comments: [
      { id: uuidv4(), userId: DEFAULT_USERS[1].id, username: DEFAULT_USERS[1].username, avatarUrl: DEFAULT_USERS[1].avatarUrl, text: 'Indeed. The old ways are crumbling.', timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString() },
      { id: uuidv4(), userId: DEFAULT_USERS[2].id, username: DEFAULT_USERS[2].username, avatarUrl: DEFAULT_USERS[2].avatarUrl, text: 'Transparency is key.', timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString() },
    ],
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    tags: ['Decentralize', 'TruthWillOut', 'Sovereign'],
  },
  {
    id: uuidv4(),
    userId: DEFAULT_USERS[1].id,
    username: DEFAULT_USERS[1].username,
    userAvatarUrl: DEFAULT_USERS[1].avatarUrl,
    imageUrl: 'https://picsum.photos/seed/post2_code/600/600',
    caption: 'Code is law. Building the new digital frontier. Who is with me?',
    contentBody: "Through cryptography and distributed systems, we can create platforms that are resistant to censorship and control. This image represents the digital scaffold of a freer future. It's not just about writing lines of code, it's about embedding principles of autonomy into the very fabric of our digital interactions.",
    likes: 101,
    comments: [
      { id: uuidv4(), userId: DEFAULT_USERS[0].id, username: DEFAULT_USERS[0].username, avatarUrl: DEFAULT_USERS[0].avatarUrl, text: 'Count me in. Open source everything.', timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString() },
    ],
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
    tags: ['CodeIsLaw', 'OpenSource', 'DigitalFreedom'],
  },
  {
    id: uuidv4(),
    userId: DEFAULT_USERS[2].id,
    username: DEFAULT_USERS[2].username,
    userAvatarUrl: DEFAULT_USERS[2].avatarUrl,
    imageUrl: 'https://picsum.photos/seed/post3_knowledge/800/500',
    caption: 'Uncover hidden narratives. Seek what they don\'t want you to find. #ForbiddenKnowledge',
    contentBody: "There's a wealth of information out there, often obscured or deliberately hidden. True sovereignty comes from the relentless pursuit of knowledge, even when it challenges established norms. This path isn't easy, but the truth has a way of surfacing.",
    likes: 84,
    comments: [],
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    tags: ['TruthSeeking', 'HiddenNarratives'],
  },
];
