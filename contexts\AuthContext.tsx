
import React, { createContext, useState, ReactNode, useMemo, useCallback, useEffect } from 'react';
import { User, ADMIN_USER_ID, LoginScreenConfig, AdminMessage } from '../types';
import { DEFAULT_USERS, DEFAULT_LOGIN_SCREEN_CONFIG, LOGIN_SCREEN_CONFIG_KEY } from '../constants';
import { v4 as uuidv4 } from 'uuid';

interface AuthContextType {
  currentUser: User | null;
  users: User[];
  isAdmin: boolean;
  loginAs: (userId: string) => void;
  logout: () => void;
  signUpUser: (username: string, avatarSeed?: string) => Promise<User | null>;
  approveUser: (userId: string) => void;
  toggleUserActivation: (userId: string) => void;
  removeUser: (userId: string) => void;
  loginScreenConfig: LoginScreenConfig;
  updateLoginScreenConfig: (newConfig: Partial<LoginScreenConfig>) => void;
  adminMessages: AdminMessage[];
  sendMessageToAdmin: (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => Promise<void>;
  markMessageAsRead: (messageId: string) => void;
  deleteAdminMessage: (messageId: string) => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>(DEFAULT_USERS);
  const [currentUser, setCurrentUser] = useState<User | null>(null); // Default to null

  const [loginScreenConfig, setLoginScreenConfig] = useState<LoginScreenConfig>(() => {
    try {
      const storedConfig = localStorage.getItem(LOGIN_SCREEN_CONFIG_KEY);
      return storedConfig ? JSON.parse(storedConfig) : DEFAULT_LOGIN_SCREEN_CONFIG;
    } catch (error) {
      console.error("Error loading login screen config from localStorage:", error);
      return DEFAULT_LOGIN_SCREEN_CONFIG;
    }
  });

  const [adminMessages, setAdminMessages] = useState<AdminMessage[]>([]);

  const isAdmin = useMemo(() => currentUser?.id === ADMIN_USER_ID, [currentUser]);

  const loginAs = useCallback((userId: string) => {
    const userToLogin = users.find(u => u.id === userId);
    if (userToLogin) {
      if (userToLogin.id === ADMIN_USER_ID || (userToLogin.isActive && !userToLogin.isPendingApproval)) {
        setCurrentUser(userToLogin);
      } else {
         alert(`User ${userToLogin.username} is not active or pending approval and cannot log in.`);
      }
    } else {
      console.warn(`User with ID ${userId} not found.`);
       alert(`Login failed: User ID ${userId} not found.`);
    }
  }, [users]);

  const logout = useCallback(() => {
    setCurrentUser(null);
  }, []);

  const signUpUser = useCallback(async (username: string, avatarSeed?: string): Promise<User | null> => {
    if (users.find(u => u.username.toLowerCase() === username.toLowerCase())) {
      alert('Username already taken.');
      return null;
    }
    const newUser: User = {
      id: uuidv4(),
      username,
      avatarUrl: `https://picsum.photos/seed/${avatarSeed || username.replace(/\s+/g, '')}/100/100`,
      isActive: false,
      isPendingApproval: true,
    };
    setUsers(prevUsers => [...prevUsers, newUser]);
    return newUser;
  }, [users]);

  const approveUser = useCallback((userId: string) => {
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, isActive: true, isPendingApproval: false } : user
      )
    );
  }, []);

  const toggleUserActivation = useCallback((userId: string) => {
    if (userId === ADMIN_USER_ID) {
        alert("Cannot deactivate the admin account.");
        return;
    }
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, isActive: !user.isActive, isPendingApproval: user.isActive ? false : user.isPendingApproval } : user
      )
    );
    if (currentUser?.id === userId) {
      const user = users.find(u => u.id === userId);
      if(user && !user.isActive) {
           logout();
      }
    }
  }, [currentUser, users, logout]);

  const removeUser = useCallback((userId: string) => {
    if (userId === ADMIN_USER_ID) {
        alert("Cannot remove the admin account.");
        return;
    }
    setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
    if (currentUser?.id === userId) {
      logout();
    }
  }, [currentUser, logout]);

  const updateLoginScreenConfig = useCallback((newConfig: Partial<LoginScreenConfig>) => {
    setLoginScreenConfig(prevConfig => {
      const updated = { ...prevConfig, ...newConfig };
      try {
        localStorage.setItem(LOGIN_SCREEN_CONFIG_KEY, JSON.stringify(updated));
      } catch (error) {
        console.error("Error saving login screen config to localStorage:", error);
      }
      return updated;
    });
  }, []);

  const sendMessageToAdmin = useCallback(async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => {
    const newMessage: AdminMessage = {
      ...messageData,
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      isRead: false,
    };
    setAdminMessages(prevMessages => [newMessage, ...prevMessages]);
  }, []);

  const markMessageAsRead = useCallback((messageId: string) => {
    setAdminMessages(prevMessages =>
      prevMessages.map(message =>
        message.id === messageId ? { ...message, isRead: true } : message
      )
    );
  }, []);

  const deleteAdminMessage = useCallback((messageId: string) => {
    setAdminMessages(prevMessages => prevMessages.filter(message => message.id !== messageId));
  }, []);

  const contextValue = useMemo(() => ({
    currentUser,
    users,
    isAdmin,
    loginAs,
    logout,
    signUpUser,
    approveUser,
    toggleUserActivation,
    removeUser,
    loginScreenConfig,
    updateLoginScreenConfig,
    adminMessages,
    sendMessageToAdmin,
    markMessageAsRead,
    deleteAdminMessage,
  }), [currentUser, users, isAdmin, loginAs, logout, signUpUser, approveUser, toggleUserActivation, removeUser, loginScreenConfig, updateLoginScreenConfig, adminMessages, sendMessageToAdmin, markMessageAsRead, deleteAdminMessage]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
