<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>S3Kt0R-Gram</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Inter', 'sans-serif'],
            'logo': ['"Major Mono Display"', 'monospace'], 
          },
          colors: {
            'brand-primary': '#00DC82', 
            'brand-secondary': '#00B86B', 
            'neutral-base': '#121212',    
            'neutral-surface': '#1E1E1E', 
            'neutral-border': '#333333',  
            'neutral-muted': '#A0A0A0',   
            'neutral-100': '#FFFFFF',    
            'neutral-200': '#E0E0E0',    
            'accent-error': '#FF4D4D',   
            'accent-warning': '#FFA500', 
            'accent-success': '#32CD32',
            'brand-purple': '#A020F0', // Added for charts & matrix
          }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Major+Mono+Display&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "uuid": "https://esm.sh/uuid@^11.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "chart.js": "https://esm.sh/chart.js@4.4.3/auto" 
  }
}
</script>
<style>
  #matrix-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1; /* Behind all other content */
  }
  body {
    background-color: #121212; /* neutral-base, Fallback if canvas fails or for brief moments */
    color: var(--neutral-100);
  }
  #root {
    position: relative; /* Ensure content is layered above the canvas */
    z-index: 1;
  }
  /* Custom scrollbar for a more thematic feel */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #1E1E1E; /* neutral-surface */
  }
  ::-webkit-scrollbar-thumb {
    background: #00DC82; /* brand-primary */
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #00B86B; /* brand-secondary */
  }
</style>
</head>
<body>
  <canvas id="matrix-canvas"></canvas>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
