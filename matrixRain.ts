// S3Kt0R-Gram Matrix Rain Effect

// Characters to use in the rain - Katakana, Alphanumeric, and some custom symbols
const KATAKANA_CHARS = "アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン";
const ALPHANUMERIC_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
const SYMBOL_CHARS = "ΣΠΔΦΨΩ∞≈≠≤≥∴∵∫∮∝¬⊕⊗⊘⊙⊚⊛⊝⊞⊟⊠⊡⋄⋆⋅∘∙√"; // Some thematic symbols
const ALL_CHARS = KATAKANA_CHARS + ALPHANUMERIC_CHARS + SYMBOL_CHARS;

const RAIN_GREEN = '#00DC82'; // brand-primary
const RAIN_PURPLE = '#A020F0'; // A vibrant purple
const HIGHLIGHT_COLOR = '#E0E0E0'; // neutral-200 (light grey/white)
const BACKGROUND_FILL_COLOR = 'rgba(18, 18, 18, 0.05)'; // neutral-base with low alpha for trails

const FONT_SIZE = 16;
const MIN_SPEED = 1;
const MAX_SPEED = 3;
const SPAWN_RATE = 0.97; // Chance for a stream to respawn in an empty column top

interface Drop {
  x: number;
  y: number;
  speed: number;
  text: string;
  isHighlighted: boolean;
  color: string;
}

let canvas: HTMLCanvasElement;
let ctx: CanvasRenderingContext2D;
let columns: number;
let drops: Drop[];
let animationFrameId: number;

function setupCanvas() {
  canvas = document.getElementById('matrix-canvas') as HTMLCanvasElement;
  if (!canvas) {
    console.error("Matrix canvas not found!");
    return false;
  }
  ctx = canvas.getContext('2d')!;
  if (!ctx) {
    console.error("Could not get 2D context for Matrix canvas!");
    return false;
  }
  return true;
}

function resizeCanvas() {
  if (!canvas || !ctx) return;
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  columns = Math.floor(canvas.width / FONT_SIZE);
  initializeDrops();
}

function initializeDrops() {
  drops = [];
  for (let i = 0; i < columns; i++) {
    drops[i] = createDrop(i * FONT_SIZE, true); // Initial drops start at top
  }
}

function createDrop(x: number, startAtTop: boolean = false): Drop {
  const speed = Math.random() * (MAX_SPEED - MIN_SPEED) + MIN_SPEED;
  const y = startAtTop ? 0 : Math.random() * -canvas.height; // Start some off-screen initially
  const isHighlighted = Math.random() < 0.15; // Small chance for the first char to be highlighted
  const color = Math.random() < 0.75 ? RAIN_GREEN : RAIN_PURPLE; // 75% green, 25% purple streams

  return {
    x,
    y,
    speed,
    text: ALL_CHARS[Math.floor(Math.random() * ALL_CHARS.length)],
    isHighlighted,
    color,
  };
}

function draw() {
  if (!ctx || !canvas) return;

  // Background fill for trail effect
  ctx.fillStyle = BACKGROUND_FILL_COLOR;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  ctx.font = `${FONT_SIZE}px monospace`;

  for (let i = 0; i < drops.length; i++) {
    const drop = drops[i];
    
    // Draw character
    if (drop.isHighlighted && drop.y < FONT_SIZE * 5) { // Highlight only for a short duration at the start of stream
        ctx.fillStyle = HIGHLIGHT_COLOR;
    } else {
        ctx.fillStyle = drop.color;
    }
    ctx.fillText(drop.text, drop.x, drop.y);

    // Move drop
    drop.y += drop.speed;

    // Reset drop if it goes off screen or randomly
    if (drop.y > canvas.height && Math.random() > SPAWN_RATE) {
      drops[i] = createDrop(drop.x);
      drops[i].y = 0; // Ensure it resets to the very top
      drops[i].isHighlighted = Math.random() < 0.15; // Re-evaluate highlight on reset
    } else if (drop.y > canvas.height) {
        // If not respawning immediately, just move it off screen to wait
        drops[i].y = canvas.height + FONT_SIZE * 2; // Ensure it's fully off
    }
    
    // Update character randomly
    if (Math.random() > 0.95) { // Less frequent char change
        drop.text = ALL_CHARS[Math.floor(Math.random() * ALL_CHARS.length)];
    }
  }

  animationFrameId = requestAnimationFrame(draw);
}

function startMatrixRain() {
  if (!setupCanvas()) return;
  
  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);
  
  // Ensure drops are initialized before first draw
  if (!drops || drops.length !== columns) {
      initializeDrops();
  }

  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  draw();
}

function stopMatrixRain() {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  window.removeEventListener('resize', resizeCanvas);
}

// Export functions if needed, or just call startMatrixRain from index.tsx
export { startMatrixRain, stopMatrixRain };
