import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';

type ImageInputType = 'url' | 'upload';

const CreatePostPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { addPost } = usePosts();
  const navigate = useNavigate();

  const [imageInputType, setImageInputType] = useState<ImageInputType>('url');
  const [imageUrl, setImageUrl] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [caption, setCaption] = useState('');
  const [contentBody, setContentBody] = useState('');
  const [tags, setTags] = useState(''); // Comma-separated

  useEffect(() => {
    if (!currentUser) {
      navigate('/'); // Redirect if not logged in
    }
  }, [currentUser, navigate]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
        setImageUrl(reader.result as string); // Store base64 data as imageUrl
      };
      reader.readAsDataURL(file);
    } else {
      setImageFile(null);
      setImagePreview(null);
      if (imageInputType === 'upload') setImageUrl('');
    }
  };
  
  const handleImageInputTypeChange = (type: ImageInputType) => {
    setImageInputType(type);
    setImageUrl(''); // Reset image URL when switching types
    setImageFile(null);
    setImagePreview(null);
  };

  if (!currentUser) {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-neutral-100">You must be logged in to create a post.</p>
        <button onClick={() => navigate('/')} className="mt-4 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors">
          Go Home
        </button>
      </div>
    );
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!imageUrl.trim()) {
      alert("Image is required. Provide a URL or upload a file.");
      return;
    }
     if (!caption.trim() && !contentBody.trim()) {
      alert("Either a Caption or a Post Body is required.");
      return;
    }
    
    const postData = {
      userId: currentUser.id,
      username: currentUser.username,
      userAvatarUrl: currentUser.avatarUrl,
      imageUrl,
      caption,
      contentBody,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
    };
    addPost(postData);
    navigate('/');
  };

  return (
    <div className="max-w-xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-8 text-center">Create New Post</h1>
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border">
        
        <div>
          <label className="block text-sm font-medium text-neutral-300 mb-2">Image Source</label>
          <div className="flex items-center space-x-4 mb-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input 
                type="radio" 
                name="imageInputType" 
                value="url" 
                checked={imageInputType === 'url'} 
                onChange={() => handleImageInputTypeChange('url')}
                className="form-radio h-4 w-4 text-brand-primary bg-neutral-base border-neutral-border focus:ring-brand-primary"
              />
              <span className="text-neutral-200">Use Image URL</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input 
                type="radio" 
                name="imageInputType" 
                value="upload" 
                checked={imageInputType === 'upload'} 
                onChange={() => handleImageInputTypeChange('upload')}
                className="form-radio h-4 w-4 text-brand-primary bg-neutral-base border-neutral-border focus:ring-brand-primary"
              />
              <span className="text-neutral-200">Upload Image</span>
            </label>
          </div>

          {imageInputType === 'url' && (
            <div>
              <label htmlFor="imageUrl" className="block text-sm font-medium text-neutral-300 mb-1">
                Image URL
              </label>
              <input
                type="url"
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
              />
              <p className="mt-1 text-xs text-neutral-muted">Use a service like <a href="https://picsum.photos/" target="_blank" rel="noopener noreferrer" className="text-brand-primary hover:underline">picsum.photos</a> for placeholder URLs.</p>
            </div>
          )}

          {imageInputType === 'upload' && (
            <div>
              <label htmlFor="imageUpload" className="block text-sm font-medium text-neutral-300 mb-1">
                Upload Image File
              </label>
              <input
                type="file"
                id="imageUpload"
                accept="image/*"
                onChange={handleFileChange}
                className="w-full text-sm text-neutral-300 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-brand-primary file:text-white hover:file:bg-brand-secondary cursor-pointer"
              />
            </div>
          )}
          {imagePreview && imageInputType === 'upload' && (
            <div className="mt-4">
              <p className="text-sm font-medium text-neutral-300 mb-1">Image Preview:</p>
              <img src={imagePreview} alt="Preview" className="max-h-48 w-auto rounded-md border border-neutral-border" />
            </div>
          )}
           {imageUrl && imageInputType === 'url' && (
            <div className="mt-4">
              <p className="text-sm font-medium text-neutral-300 mb-1">Image Preview (from URL):</p>
              <img src={imageUrl} alt="URL Preview" className="max-h-48 w-auto rounded-md border border-neutral-border" 
                   onError={(e) => (e.currentTarget.style.display = 'none')} 
                   onLoad={(e) => (e.currentTarget.style.display = 'block')}/>
            </div>
          )}
        </div>

        <div>
          <label htmlFor="caption" className="block text-sm font-medium text-neutral-300 mb-1">
            Caption (Short Summary)
          </label>
          <input
            id="caption"
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            maxLength={150}
            placeholder="A brief highlight or title for your post..."
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="contentBody" className="block text-sm font-medium text-neutral-300 mb-1">
            Post Body
          </label>
          <textarea
            id="contentBody"
            value={contentBody}
            onChange={(e) => setContentBody(e.target.value)}
            rows={6}
            placeholder="Share your detailed truth, insights, or story..."
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-neutral-300 mb-1">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            id="tags"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            placeholder="e.g., Truth, Sovereign, Decentralize"
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <button
          type="submit"
          className="w-full bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary"
        >
          Post as {currentUser.username}
        </button>
      </form>
    </div>
  );
};

export default CreatePostPage;