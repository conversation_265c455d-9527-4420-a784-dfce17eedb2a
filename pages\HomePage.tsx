
import React from 'react';
import PostCard from '../components/PostCard';
import { usePosts } from '../hooks/usePosts';

const HomePage: React.FC = () => {
  const { posts } = usePosts();

  return (
    <div className="max-w-2xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {posts.length === 0 && (
        <div className="text-center text-neutral-muted py-10 animate-slide-up">
          <p className="text-xl animate-text-glow">No posts yet.</p>
          <p className="hover-text-glow transition-all duration-300">Be the first to share something revolutionary!</p>
        </div>
      )}
      {posts.map((post, index) => (
        <div key={post.id} style={{ animationDelay: `${index * 0.1}s` }}>
          <PostCard post={post} />
        </div>
      ))}
    </div>
  );
};

export default HomePage;
