
import React from 'react';
import PostCard from '../components/PostCard';
import { usePosts } from '../hooks/usePosts';

const HomePage: React.FC = () => {
  const { posts } = usePosts();

  return (
    <div className="max-w-2xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {posts.length === 0 && (
        <div className="text-center text-neutral-muted py-10">
          <p className="text-xl">No posts yet.</p>
          <p>Be the first to share something revolutionary!</p>
        </div>
      )}
      {posts.map(post => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  );
};

export default HomePage;
