
import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { APP_NAME } from '../constants';
import { ADMIN_USER_ID, User } from '../types';

const LoginPage: React.FC = () => {
  const { users, loginAs, loginScreenConfig } = useAuth();
  const navigate = useNavigate();

  const handleLogin = (userId: string) => {
    loginAs(userId);
    navigate('/'); // Navigate to home after successful login attempt
  };

  const availableUsersForLogin = users.filter(u => (u.isActive && !u.isPendingApproval) || u.id === ADMIN_USER_ID);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 relative text-neutral-100">
      {/* Background Image */}
      {loginScreenConfig.imageUrl && (
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center z-0"
          style={{ backgroundImage: `url(${loginScreenConfig.imageUrl})` }}
        >
          <div 
            className="absolute inset-0 w-full h-full bg-neutral-base"
            style={{ opacity: loginScreenConfig.imageOverlayOpacity ?? 0.5 }}
          ></div>
        </div>
      )}
      
      {/* Matrix rain will be behind this due to z-indexing from index.html */}

      <div className="relative z-10 flex flex-col items-center bg-neutral-surface/80 backdrop-blur-md p-8 rounded-xl shadow-2xl border border-neutral-border max-w-lg w-full">
        <h1 className="text-5xl font-logo text-brand-primary mb-4">{APP_NAME}</h1>
        <p className="text-neutral-200 mb-8 text-center text-lg">
          {loginScreenConfig.message}
        </p>

        <div className="w-full space-y-3 mb-6">
          <p className="text-sm text-center text-neutral-muted">Select a profile to enter (Demo Login):</p>
          {availableUsersForLogin.map((user: User) => (
            <button
              key={user.id}
              onClick={() => handleLogin(user.id)}
              className="w-full flex items-center space-x-3 bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary p-3 rounded-lg transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-brand-primary"
            >
              <img src={user.avatarUrl} alt={user.username} className="w-10 h-10 rounded-full border-2 border-neutral-border" />
              <span className="font-medium text-neutral-100 text-lg">{user.username} {user.id === ADMIN_USER_ID && "(Admin)"}</span>
            </button>
          ))}
           {availableUsersForLogin.length === 0 && (
            <p className="text-center text-neutral-muted">No active users available for login. Admin may need to approve users.</p>
          )}
        </div>

        <p className="text-neutral-300 text-sm">
          Need an identity?{' '}
          <Link to="/signup" className="font-medium text-brand-primary hover:text-brand-secondary hover:underline">
            Sign Up Here
          </Link>
        </p>
        <p className="text-xs text-neutral-muted mt-8">&copy; {new Date().getFullYear()} {APP_NAME}</p>
      </div>
    </div>
  );
};

export default LoginPage;
