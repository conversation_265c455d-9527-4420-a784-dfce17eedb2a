
import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { APP_NAME } from '../constants';
import { ADMIN_USER_ID, User } from '../types';

const LoginPage: React.FC = () => {
  const { users, loginAs, loginScreenConfig } = useAuth();
  const navigate = useNavigate();
  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showDemoLinks, setShowDemoLinks] = React.useState(false);

  const handleLogin = (userId: string) => {
    loginAs(userId);
    navigate('/'); // Navigate to home after successful login attempt
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For demo purposes, we'll just show a message since this is a mock app
    alert('This is a demo app. Please use the demo login links below or sign up for a new account.');
  };

  const availableUsersForLogin = users.filter(u => (u.isActive && !u.isPendingApproval) || u.id === ADMIN_USER_ID);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 relative text-neutral-100">
      {/* Background Image */}
      {loginScreenConfig.imageUrl && (
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center z-0"
          style={{ backgroundImage: `url(${loginScreenConfig.imageUrl})` }}
        >
          <div
            className="absolute inset-0 w-full h-full bg-neutral-base"
            style={{ opacity: loginScreenConfig.imageOverlayOpacity ?? 0.5 }}
          ></div>
        </div>
      )}

      {/* Matrix rain will be behind this due to z-indexing from index.html */}

      <div className="relative z-10 flex flex-col items-center bg-neutral-surface/80 backdrop-blur-md p-8 rounded-xl shadow-2xl border border-neutral-border max-w-lg w-full animate-pulse-glow">
        <h1 className="text-5xl font-logo text-brand-primary mb-4 animate-text-glow">{APP_NAME}</h1>
        <p className="text-neutral-200 mb-8 text-center text-lg">
          {loginScreenConfig.message}
        </p>

        {/* Main Login Form */}
        <form onSubmit={handleFormSubmit} className="w-full space-y-4 mb-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-neutral-300 mb-1">
              Username
            </label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter your username"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-neutral-300 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25"
          >
            Login
          </button>
        </form>

        <p className="text-neutral-300 text-sm mb-4">
          Need an identity?{' '}
          <Link to="/signup" className="font-medium text-brand-primary hover:text-brand-secondary hover:underline transition-colors duration-200">
            Sign Up Here
          </Link>
        </p>

        {/* Demo Login Links */}
        <div className="w-full border-t border-neutral-border pt-4">
          <button
            onClick={() => setShowDemoLinks(!showDemoLinks)}
            className="text-xs text-neutral-muted hover:text-brand-primary transition-colors duration-200 mb-2"
          >
            {showDemoLinks ? 'Hide' : 'Show'} Demo Login Links
          </button>

          {showDemoLinks && (
            <div className="space-y-2">
              <p className="text-xs text-center text-neutral-muted mb-2">Quick Demo Access:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {availableUsersForLogin.map((user: User) => (
                  <button
                    key={user.id}
                    onClick={() => handleLogin(user.id)}
                    className="text-xs bg-neutral-base hover:bg-brand-primary/20 border border-neutral-border hover:border-brand-primary px-2 py-1 rounded transition-all duration-200 text-neutral-200 hover:text-brand-primary"
                  >
                    {user.username} {user.id === ADMIN_USER_ID && "(Admin)"}
                  </button>
                ))}
              </div>
              {availableUsersForLogin.length === 0 && (
                <p className="text-xs text-center text-neutral-muted">No active users available for demo login.</p>
              )}
            </div>
          )}
        </div>

        <p className="text-xs text-neutral-muted mt-6">&copy; {new Date().getFullYear()} {APP_NAME}</p>
      </div>
    </div>
  );
};

export default LoginPage;
