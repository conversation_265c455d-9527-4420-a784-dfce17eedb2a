
import React, { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import PostCard from '../components/PostCard';
import { Link, useNavigate } from 'react-router-dom';

const ProfilePage: React.FC = () => {
  const { currentUser } = useAuth();
  const { posts } = usePosts();
  const navigate = useNavigate();

  useEffect(() => {
    if (!currentUser) {
      navigate('/'); // Redirect to home if no user is logged in
    }
  }, [currentUser, navigate]);

  if (!currentUser) {
    return (
       <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-neutral-100">No user selected or you are not logged in.</p>
        <p className="text-neutral-muted">Please log in to view a profile.</p>
        <Link to="/" className="mt-4 inline-block bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors">
          Go Home
        </Link>
      </div>
    );
  }

  const userPosts = posts.filter(post => post.userId === currentUser.id);

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <header className="flex flex-col sm:flex-row items-center mb-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border animate-slide-up hover-glow cyber-border">
        <img
          src={currentUser.avatarUrl}
          alt={currentUser.username}
          className="w-24 h-24 sm:w-32 sm:h-32 rounded-full mr-0 sm:mr-8 mb-4 sm:mb-0 border-4 border-brand-primary hover-scale transition-transform duration-200 animate-pulse-glow"
        />
        <div>
          <h1 className="text-3xl sm:text-4xl font-bold text-neutral-100 text-center sm:text-left animate-text-glow hover-text-glow">{currentUser.username}</h1>
          <p className="text-neutral-muted mt-1 text-center sm:text-left">ID: {currentUser.id}</p>
          <div className="mt-4 flex space-x-6 justify-center sm:justify-start">
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">{userPosts.length}</span>
              <p className="text-neutral-muted text-sm">Posts</p>
            </div>
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">0</span>
              <p className="text-neutral-muted text-sm">Followers</p>
            </div>
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">0</span>
              <p className="text-neutral-muted text-sm">Following</p>
            </div>
          </div>
           {currentUser.id === 'admin_s3kt0r_truth' && (
             <p className="mt-3 text-sm bg-red-500/20 text-red-400 px-3 py-1 rounded-md inline-block">ADMINISTRATOR ACCESS</p>
           )}
            {!currentUser.isActive && (
                 <p className={`mt-3 text-sm px-3 py-1 rounded-md inline-block ${currentUser.isPendingApproval ? 'bg-accent-warning/20 text-accent-warning' : 'bg-accent-error/20 text-accent-error'}`}>
                    {currentUser.isPendingApproval ? 'ACCOUNT PENDING APPROVAL' : 'ACCOUNT INACTIVE'}
                 </p>
            )}
        </div>
      </header>

      <h2 className="text-2xl font-semibold text-neutral-100 mb-6 hover-text-glow transition-all duration-300">Your Posts</h2>
      {userPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> {/* More compact grid for profile */}
          {userPosts.map((post, index) => (
            <div key={post.id} style={{ animationDelay: `${index * 0.05}s` }}>
              <PostCard post={post} isCompact={true} />
            </div>
          ))}
        </div>
      ) : (
        <p className="text-neutral-muted text-center py-10 animate-slide-up">You haven't made any posts yet. <Link to="/create" className="text-brand-primary hover:underline hover-text-glow transition-all duration-200">Share your truth!</Link></p>
      )}
    </div>
  );
};

export default ProfilePage;
